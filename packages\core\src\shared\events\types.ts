/**
 * 事件名称常量
 */
export const EVENT_NAMES = {
    HEARTBEAT_REQUEST: "yesimbot/heartbeat",
    USER_JOINED: "yesimbot/user_joined",
    USER_LEFT: "yesimbot/user_left",
    MESSAGE_SENT: "yesimbot/message_sent",
    SYSTEM_NOTIFICATION: "yesimbot/system_notification",
    CHANNEL_TOPIC_CHANGED: "yesimbot/channel_topic_changed",
    HEARTBEAT_TICK: "yesimbot/heartbeat_tick",
} as const;

export type EventName = (typeof EVENT_NAMES)[keyof typeof EVENT_NAMES];

/**
 * 基础事件结构
 */
interface BaseEvent {
    id: number; // 自增 ID
    type: string;
    timestamp: Date;
}

/**
 * 成员信息接口（用于事件）
 */
export interface EventMember {
    id: string;
    name: string;
    nick?: string;
    role?: string;
    avatar?: string;
    [key: string]: unknown;
}

/**
 * 用户加入事件
 */
export interface UserJoinedEvent extends BaseEvent {
    type: "user_joined";
    actor: EventMember; // 操作者 (可能是系统或其他成员)
    user: EventMember; // 加入的成员
    note?: string;
}

/**
 * 用户离开事件
 */
export interface UserLeftEvent extends BaseEvent {
    type: "user_left";
    actor: EventMember;
    user: EventMember;
    reason?: string;
}

/**
 * 消息事件
 */
export interface MessageEvent extends BaseEvent {
    type: "message";
    messageId: string;
    sender: EventMember;
    content: string;
}

/**
 * 系统通知事件
 */
export interface SystemNotificationEvent extends BaseEvent {
    type: "system_notification";
    content: string;
}

/**
 * 频道主题变更事件
 */
export interface ChannelTopicChangedEvent extends BaseEvent {
    type: "channel_topic_changed";
    actor: EventMember;
    oldTopic?: string;
    newTopic: string;
}

/**
 * 心跳事件
 */
export interface HeartbeatTickEvent extends BaseEvent {
    type: "heartbeat_tick";
    note?: string;
}

/**
 * 频道事件联合类型
 */
export type ChannelEvent =
    | UserJoinedEvent
    | UserLeftEvent
    | MessageEvent
    | SystemNotificationEvent
    | ChannelTopicChangedEvent
    | HeartbeatTickEvent;

/**
 * 事件处理器接口
 */
export interface EventHandler<T extends ChannelEvent = ChannelEvent> {
    handle(event: T): Promise<void> | void;
}

/**
 * 事件发射器接口
 */
export interface EventEmitter {
    emit<T extends ChannelEvent>(event: T): Promise<void>;
    on<T extends ChannelEvent>(eventType: T["type"], handler: EventHandler<T>): void;
    off<T extends ChannelEvent>(eventType: T["type"], handler: EventHandler<T>): void;
}
